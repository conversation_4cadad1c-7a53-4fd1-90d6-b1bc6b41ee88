{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeApp-2.0_last\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\ShiftSummarySection.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useEffect, useState, useRef } from \"react\";\nimport Loading from \"../common/Loading\";\nimport { API_URL } from \"../common/fetchData/apiConfig\";\n\n// const SmallStat = ({ label, value }) => (\n//   <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between\">\n//     <div className=\"text-sm font-medium text-gray-700 dark:text-gray-200\">{label}</div>\n//     <div className=\"flex items-center gap-2 text-gray-900 dark:text-gray-100\">\n//       <span>👤</span>\n//       <span className=\"font-semibold\">{String(value).padStart(2, \"0\")}</span>\n//     </div>\n//   </div>\n// ); \nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmallStat = ({\n  label,\n  value\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-start gap-1 p-3 bg-gray-50 dark:bg-gray-700/40 rounded-xl border border-gray-200 dark:border-gray-700 text-sm\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"font-medium text-gray-700 dark:text-gray-200\",\n    children: label\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center gap-1 text-gray-900 dark:text-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      className: \"text-gray-500 dark:text-gray-400\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"12\",\n        cy: \"7\",\n        r: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"font-semibold text-lg\",\n      children: String(value).padStart(2, \"0\")\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 15,\n  columnNumber: 3\n}, this);\n_c = SmallStat;\nconst ShiftCard = ({\n  shift\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4\",\n  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\",\n    children: [shift.name, \" Shift\"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-3 gap-3\",\n    children: [/*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total Designer\",\n      value: shift.designer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total Developer\",\n      value: shift.developer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total QA\",\n      value: shift.qa\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 29,\n  columnNumber: 3\n}, this);\n\n// Infinite Slider Component for Shifts\n_c2 = ShiftCard;\nconst InfiniteShiftSlider = ({\n  shifts\n}) => {\n  _s();\n  const sliderRef = useRef(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startX, setStartX] = useState(0);\n  const [scrollLeft, setScrollLeft] = useState(0);\n  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);\n\n  // Create infinite loop by duplicating shifts\n  const infiniteShifts = [...shifts, ...shifts, ...shifts, ...shifts];\n\n  // Auto-scroll functionality\n  useEffect(() => {\n    if (!autoScrollEnabled || isDragging || shifts.length === 0) return;\n    const interval = setInterval(() => {\n      if (sliderRef.current) {\n        const slider = sliderRef.current;\n        const cardWidth = 320; // Approximate card width + gap\n        const maxScroll = cardWidth * shifts.length;\n\n        // Smooth infinite loop\n        if (slider.scrollLeft >= maxScroll) {\n          slider.scrollTo({\n            left: 0,\n            behavior: 'auto'\n          });\n        } else {\n          slider.scrollLeft += 0.5;\n        }\n      }\n    }, 20); // Faster interval for smoother animation\n\n    return () => clearInterval(interval);\n  }, [autoScrollEnabled, isDragging, shifts.length]);\n\n  // Mouse drag handlers\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    setAutoScrollEnabled(false);\n    setStartX(e.pageX - sliderRef.current.offsetLeft);\n    setScrollLeft(sliderRef.current.scrollLeft);\n  };\n  const handleMouseMove = e => {\n    if (!isDragging) return;\n    e.preventDefault();\n    const x = e.pageX - sliderRef.current.offsetLeft;\n    const walk = (x - startX) * 2;\n    sliderRef.current.scrollLeft = scrollLeft - walk;\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n    setTimeout(() => setAutoScrollEnabled(true), 2000); // Resume auto-scroll after 2s\n  };\n\n  // Touch handlers for mobile\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setAutoScrollEnabled(false);\n    setStartX(e.touches[0].pageX - sliderRef.current.offsetLeft);\n    setScrollLeft(sliderRef.current.scrollLeft);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const x = e.touches[0].pageX - sliderRef.current.offsetLeft;\n    const walk = (x - startX) * 2;\n    sliderRef.current.scrollLeft = scrollLeft - walk;\n  };\n  const handleTouchEnd = () => {\n    setIsDragging(false);\n    setTimeout(() => setAutoScrollEnabled(true), 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: sliderRef,\n      className: \"flex gap-4 overflow-x-auto scrollbar-hide cursor-grab active:cursor-grabbing\",\n      style: {\n        scrollbarWidth: 'none',\n        msOverflowStyle: 'none',\n        WebkitOverflowScrolling: 'touch'\n      },\n      onMouseDown: handleMouseDown,\n      onMouseMove: handleMouseMove,\n      onMouseUp: handleMouseUp,\n      onMouseLeave: handleMouseUp,\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      children: infiniteShifts.map((shift, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0 w-80 sm:w-72 md:w-80\",\n        children: /*#__PURE__*/_jsxDEV(ShiftCard, {\n          shift: shift\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)\n      }, `${shift.name}-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 to-transparent pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 to-transparent pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(InfiniteShiftSlider, \"iCaoEGp6Dctr2xB2kfOrXVBkGeo=\");\n_c3 = InfiniteShiftSlider;\nconst ShiftSummarySection = () => {\n  _s2();\n  const [shifts, setShifts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchShiftData = async () => {\n      const token = localStorage.getItem(\"token\");\n      if (!token) {\n        setError(\"No auth token found\");\n        setLoading(false);\n        return;\n      }\n      try {\n        setLoading(true);\n\n        // Fetch users data to calculate role counts per shift\n        const usersResponse = await fetch(`${API_URL}/list/users-by-default-team`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n          }\n        });\n        let usersData = [];\n        if (usersResponse.ok) {\n          usersData = await usersResponse.json();\n        }\n\n        // Fetch schedule data to get actual shift assignments\n        const scheduleResponse = await fetch(`${API_URL}/schedule-data`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n          }\n        });\n        let scheduleData = [];\n        if (scheduleResponse.ok) {\n          const scheduleResult = await scheduleResponse.json();\n          scheduleData = (scheduleResult === null || scheduleResult === void 0 ? void 0 : scheduleResult.data) || [];\n        }\n\n        // Fetch time cards data to get shift-based work data\n        let timeCardsData = [];\n        try {\n          const timeCardsResponse = await fetch(`${API_URL}/time-cards-data?per_page=1000`, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json',\n              'Accept': 'application/json'\n            }\n          });\n          if (timeCardsResponse.ok) {\n            var _timeCardsResult$data;\n            const timeCardsResult = await timeCardsResponse.json();\n            // Handle different possible response structures\n            if (Array.isArray(timeCardsResult)) {\n              timeCardsData = timeCardsResult;\n            } else if (timeCardsResult !== null && timeCardsResult !== void 0 && (_timeCardsResult$data = timeCardsResult.data) !== null && _timeCardsResult$data !== void 0 && _timeCardsResult$data.data && Array.isArray(timeCardsResult.data.data)) {\n              // Paginated response: { data: { data: [...], current_page: 1, ... } }\n              timeCardsData = timeCardsResult.data.data;\n            } else if (timeCardsResult !== null && timeCardsResult !== void 0 && timeCardsResult.data && Array.isArray(timeCardsResult.data)) {\n              timeCardsData = timeCardsResult.data;\n            } else if (timeCardsResult !== null && timeCardsResult !== void 0 && timeCardsResult.timeCards && Array.isArray(timeCardsResult.timeCards)) {\n              timeCardsData = timeCardsResult.timeCards;\n            } else {\n              console.warn('Unexpected time cards response structure:', timeCardsResult);\n              timeCardsData = [];\n            }\n            console.log(\"Time Cards Data:\", timeCardsData);\n          }\n        } catch (timeCardError) {\n          console.warn('Failed to fetch time cards data:', timeCardError);\n          timeCardsData = [];\n        }\n\n        // Process shift data with real user counts\n        const processedShifts = [];\n        const shiftNames = ['Evening', 'Morning', 'Night', 'Custom'];\n        shiftNames.forEach(shiftName => {\n          let shiftUsers = [];\n          if (shiftName === 'Custom') {\n            // For custom shift, get users from schedule data with custom shift names\n            const customSchedules = scheduleData.filter(schedule => schedule.shift_name && !['Evening', 'Morning', 'Night'].includes(schedule.shift_name));\n            const customUserIds = customSchedules.map(schedule => schedule.user_id).filter(Boolean);\n            shiftUsers = usersData.filter(user => customUserIds.includes(user.id));\n          } else {\n            // For standard shifts, try to get from schedule data first\n            const shiftSchedules = scheduleData.filter(schedule => {\n              var _schedule$shift_name;\n              return ((_schedule$shift_name = schedule.shift_name) === null || _schedule$shift_name === void 0 ? void 0 : _schedule$shift_name.toLowerCase()) === shiftName.toLowerCase();\n            });\n            if (shiftSchedules.length > 0) {\n              const shiftUserIds = shiftSchedules.map(schedule => schedule.user_id).filter(Boolean);\n              shiftUsers = usersData.filter(user => shiftUserIds.includes(user.id));\n            } else {\n              // Fallback: distribute users across shifts based on time cards or index\n              const shiftTimeCards = Array.isArray(timeCardsData) ? timeCardsData.filter(timeCard => {\n                if (timeCard.shift_id) {\n                  // If shift_id exists, try to match with schedule\n                  const matchingSchedule = scheduleData.find(schedule => {\n                    var _schedule$shift_name2;\n                    return schedule.id === timeCard.shift_id && ((_schedule$shift_name2 = schedule.shift_name) === null || _schedule$shift_name2 === void 0 ? void 0 : _schedule$shift_name2.toLowerCase()) === shiftName.toLowerCase();\n                  });\n                  return !!matchingSchedule;\n                }\n                return false;\n              }) : [];\n              if (shiftTimeCards.length > 0) {\n                const shiftUserIds = [...new Set(shiftTimeCards.map(tc => tc.user_id))];\n                shiftUsers = usersData.filter(user => shiftUserIds.includes(user.id));\n              } else {\n                // Final fallback: distribute by index\n                shiftUsers = usersData.filter((_, index) => {\n                  if (shiftName === 'Evening') return index % 3 === 0;\n                  if (shiftName === 'Morning') return index % 3 === 1;\n                  if (shiftName === 'Night') return index % 3 === 2;\n                  return false;\n                });\n              }\n            }\n          }\n\n          // Count by designation\n          const designerCount = shiftUsers.filter(user => {\n            var _user$designations, _user$designations$, _user$designations$$n, _user$designations2, _user$designations2$, _user$designations2$$;\n            return ((_user$designations = user.designations) === null || _user$designations === void 0 ? void 0 : (_user$designations$ = _user$designations[0]) === null || _user$designations$ === void 0 ? void 0 : (_user$designations$$n = _user$designations$.name) === null || _user$designations$$n === void 0 ? void 0 : _user$designations$$n.toLowerCase().includes('designer')) || ((_user$designations2 = user.designations) === null || _user$designations2 === void 0 ? void 0 : (_user$designations2$ = _user$designations2[0]) === null || _user$designations2$ === void 0 ? void 0 : (_user$designations2$$ = _user$designations2$.name) === null || _user$designations2$$ === void 0 ? void 0 : _user$designations2$$.toLowerCase().includes('design'));\n          }).length;\n          const developerCount = shiftUsers.filter(user => {\n            var _user$designations3, _user$designations3$, _user$designations3$$, _user$designations4, _user$designations4$, _user$designations4$$, _user$designations5, _user$designations5$, _user$designations5$$;\n            return ((_user$designations3 = user.designations) === null || _user$designations3 === void 0 ? void 0 : (_user$designations3$ = _user$designations3[0]) === null || _user$designations3$ === void 0 ? void 0 : (_user$designations3$$ = _user$designations3$.name) === null || _user$designations3$$ === void 0 ? void 0 : _user$designations3$$.toLowerCase().includes('developer')) || ((_user$designations4 = user.designations) === null || _user$designations4 === void 0 ? void 0 : (_user$designations4$ = _user$designations4[0]) === null || _user$designations4$ === void 0 ? void 0 : (_user$designations4$$ = _user$designations4$.name) === null || _user$designations4$$ === void 0 ? void 0 : _user$designations4$$.toLowerCase().includes('engineer')) || ((_user$designations5 = user.designations) === null || _user$designations5 === void 0 ? void 0 : (_user$designations5$ = _user$designations5[0]) === null || _user$designations5$ === void 0 ? void 0 : (_user$designations5$$ = _user$designations5$.name) === null || _user$designations5$$ === void 0 ? void 0 : _user$designations5$$.toLowerCase().includes('dev'));\n          }).length;\n          const qaCount = shiftUsers.filter(user => {\n            var _user$designations6, _user$designations6$, _user$designations6$$, _user$designations7, _user$designations7$, _user$designations7$$, _user$designations8, _user$designations8$, _user$designations8$$, _user$designations9, _user$designations9$, _user$designations9$$;\n            return ((_user$designations6 = user.designations) === null || _user$designations6 === void 0 ? void 0 : (_user$designations6$ = _user$designations6[0]) === null || _user$designations6$ === void 0 ? void 0 : (_user$designations6$$ = _user$designations6$.name) === null || _user$designations6$$ === void 0 ? void 0 : _user$designations6$$.toLowerCase().includes('qa')) || ((_user$designations7 = user.designations) === null || _user$designations7 === void 0 ? void 0 : (_user$designations7$ = _user$designations7[0]) === null || _user$designations7$ === void 0 ? void 0 : (_user$designations7$$ = _user$designations7$.name) === null || _user$designations7$$ === void 0 ? void 0 : _user$designations7$$.toLowerCase().includes('quality')) || ((_user$designations8 = user.designations) === null || _user$designations8 === void 0 ? void 0 : (_user$designations8$ = _user$designations8[0]) === null || _user$designations8$ === void 0 ? void 0 : (_user$designations8$$ = _user$designations8$.name) === null || _user$designations8$$ === void 0 ? void 0 : _user$designations8$$.toLowerCase().includes('test')) || ((_user$designations9 = user.designations) === null || _user$designations9 === void 0 ? void 0 : (_user$designations9$ = _user$designations9[0]) === null || _user$designations9$ === void 0 ? void 0 : (_user$designations9$$ = _user$designations9$.name) === null || _user$designations9$$ === void 0 ? void 0 : _user$designations9$$.toLowerCase().includes('assurance'));\n          }).length;\n          processedShifts.push({\n            name: shiftName,\n            designer: designerCount || 0,\n            developer: developerCount || 0,\n            qa: qaCount || 0\n          });\n        });\n\n        // Sort by preferred order\n        const order = ['evening', 'morning', 'night', 'custom'];\n        processedShifts.sort((a, b) => order.indexOf(a.name.toLowerCase()) - order.indexOf(b.name.toLowerCase()));\n        setShifts(processedShifts);\n        setError(null);\n      } catch (err) {\n        console.error(\"Error fetching shift data:\", err);\n        setError(\"Unable to load shift data. Please try again later.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchShiftData();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 330,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-red-500 text-center py-4\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 21\n  }, this);\n  if (!shifts.length) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-gray-500 text-center py-4\",\n    children: \"No shift data available\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 332,\n    columnNumber: 30\n  }, this);\n  return /*#__PURE__*/_jsxDEV(InfiniteShiftSlider, {\n    shifts: shifts\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 334,\n    columnNumber: 10\n  }, this);\n};\n\n// Add CSS for hiding scrollbars\n_s2(ShiftSummarySection, \"uS0xAyfxMHuwOWZcB5WUiapt6FA=\");\n_c4 = ShiftSummarySection;\nconst style = document.createElement('style');\nstyle.textContent = `\n  .scrollbar-hide {\n    -ms-overflow-style: none;\n    scrollbar-width: none;\n  }\n  .scrollbar-hide::-webkit-scrollbar {\n    display: none;\n  }\n`;\nif (!document.head.querySelector('style[data-scrollbar-hide-shifts]')) {\n  style.setAttribute('data-scrollbar-hide-shifts', 'true');\n  document.head.appendChild(style);\n}\nexport default ShiftSummarySection;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SmallStat\");\n$RefreshReg$(_c2, \"ShiftCard\");\n$RefreshReg$(_c3, \"InfiniteShiftSlider\");\n$RefreshReg$(_c4, \"ShiftSummarySection\");", "map": {"version": 3, "names": ["useEffect", "useState", "useRef", "Loading", "API_URL", "jsxDEV", "_jsxDEV", "SmallStat", "label", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "cx", "cy", "r", "String", "padStart", "_c", "ShiftCard", "shift", "name", "designer", "developer", "qa", "_c2", "InfiniteShiftSlider", "shifts", "_s", "sliderRef", "isDragging", "setIsDragging", "startX", "setStartX", "scrollLeft", "setScrollLeft", "autoScrollEnabled", "setAutoScrollEnabled", "infiniteShifts", "length", "interval", "setInterval", "current", "slider", "<PERSON><PERSON><PERSON><PERSON>", "maxScroll", "scrollTo", "left", "behavior", "clearInterval", "handleMouseDown", "e", "pageX", "offsetLeft", "handleMouseMove", "preventDefault", "x", "walk", "handleMouseUp", "setTimeout", "handleTouchStart", "touches", "handleTouchMove", "handleTouchEnd", "ref", "style", "scrollbarWidth", "msOverflowStyle", "WebkitOverflowScrolling", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "onTouchStart", "onTouchMove", "onTouchEnd", "map", "index", "_c3", "ShiftSummarySection", "_s2", "setShifts", "loading", "setLoading", "error", "setError", "fetchShiftData", "token", "localStorage", "getItem", "usersResponse", "fetch", "headers", "usersData", "ok", "json", "scheduleResponse", "scheduleData", "scheduleResult", "data", "timeCardsData", "timeCardsResponse", "_timeCardsResult$data", "timeCardsResult", "Array", "isArray", "timeCards", "console", "warn", "log", "timeCardError", "processedShifts", "shiftNames", "for<PERSON>ach", "shiftName", "shiftUsers", "customSchedules", "filter", "schedule", "shift_name", "includes", "customUserIds", "user_id", "Boolean", "user", "id", "shiftSchedules", "_schedule$shift_name", "toLowerCase", "shiftUserIds", "shiftTimeCards", "timeCard", "shift_id", "matchingSchedule", "find", "_schedule$shift_name2", "Set", "tc", "_", "designerCount", "_user$designations", "_user$designations$", "_user$designations$$n", "_user$designations2", "_user$designations2$", "_user$designations2$$", "designations", "developerCount", "_user$designations3", "_user$designations3$", "_user$designations3$$", "_user$designations4", "_user$designations4$", "_user$designations4$$", "_user$designations5", "_user$designations5$", "_user$designations5$$", "qaCount", "_user$designations6", "_user$designations6$", "_user$designations6$$", "_user$designations7", "_user$designations7$", "_user$designations7$$", "_user$designations8", "_user$designations8$", "_user$designations8$$", "_user$designations9", "_user$designations9$", "_user$designations9$$", "push", "order", "sort", "a", "b", "indexOf", "err", "_c4", "document", "createElement", "textContent", "head", "querySelector", "setAttribute", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeApp-2.0_last/creativeApp-2.0_web/src/dashboard/ShiftSummarySection.jsx"], "sourcesContent": ["import { useEffect, useState, useRef } from \"react\";\r\nimport Loading from \"../common/Loading\";\r\nimport { API_URL } from \"../common/fetchData/apiConfig\";\r\n\r\n// const SmallStat = ({ label, value }) => (\r\n//   <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between\">\r\n//     <div className=\"text-sm font-medium text-gray-700 dark:text-gray-200\">{label}</div>\r\n//     <div className=\"flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n//       <span>👤</span>\r\n//       <span className=\"font-semibold\">{String(value).padStart(2, \"0\")}</span>\r\n//     </div>\r\n//   </div>\r\n// ); \r\nconst SmallStat = ({ label, value }) => (\r\n  <div className=\"flex flex-col items-start gap-1 p-3 bg-gray-50 dark:bg-gray-700/40 rounded-xl border border-gray-200 dark:border-gray-700 text-sm\">\r\n    <div className=\"font-medium text-gray-700 dark:text-gray-200\">{label}</div>\r\n    <div className=\"flex items-center gap-1 text-gray-900 dark:text-gray-100\">\r\n      {/* SVG User Icon */}\r\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"text-gray-500 dark:text-gray-400\">\r\n        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\r\n        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n      </svg>\r\n      <span className=\"font-semibold text-lg\">{String(value).padStart(2, \"0\")}</span>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ShiftCard = ({ shift }) => (\r\n  <div className=\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4\">\r\n    <h4 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\">{shift.name} Shift</h4>\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3\">\r\n      <SmallStat label=\"Total Designer\" value={shift.designer} />\r\n      <SmallStat label=\"Total Developer\" value={shift.developer} />\r\n      <SmallStat label=\"Total QA\" value={shift.qa} />\r\n    </div>\r\n  </div>\r\n);\r\n\r\n// Infinite Slider Component for Shifts\r\nconst InfiniteShiftSlider = ({ shifts }) => {\r\n  const sliderRef = useRef(null);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [startX, setStartX] = useState(0);\r\n  const [scrollLeft, setScrollLeft] = useState(0);\r\n  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);\r\n\r\n  // Create infinite loop by duplicating shifts\r\n  const infiniteShifts = [...shifts, ...shifts, ...shifts, ...shifts];\r\n\r\n  // Auto-scroll functionality\r\n  useEffect(() => {\r\n    if (!autoScrollEnabled || isDragging || shifts.length === 0) return;\r\n\r\n    const interval = setInterval(() => {\r\n      if (sliderRef.current) {\r\n        const slider = sliderRef.current;\r\n        const cardWidth = 320; // Approximate card width + gap\r\n        const maxScroll = cardWidth * shifts.length;\r\n\r\n        // Smooth infinite loop\r\n        if (slider.scrollLeft >= maxScroll) {\r\n          slider.scrollTo({ left: 0, behavior: 'auto' });\r\n        } else {\r\n          slider.scrollLeft += 0.5; \r\n        }\r\n      }\r\n    }, 20); // Faster interval for smoother animation\r\n\r\n    return () => clearInterval(interval);\r\n  }, [autoScrollEnabled, isDragging, shifts.length]);\r\n\r\n  // Mouse drag handlers\r\n  const handleMouseDown = (e) => {\r\n    setIsDragging(true);\r\n    setAutoScrollEnabled(false);\r\n    setStartX(e.pageX - sliderRef.current.offsetLeft);\r\n    setScrollLeft(sliderRef.current.scrollLeft);\r\n  };\r\n\r\n  const handleMouseMove = (e) => {\r\n    if (!isDragging) return;\r\n    e.preventDefault();\r\n    const x = e.pageX - sliderRef.current.offsetLeft;\r\n    const walk = (x - startX) * 2;\r\n    sliderRef.current.scrollLeft = scrollLeft - walk;\r\n  };\r\n\r\n  const handleMouseUp = () => {\r\n    setIsDragging(false);\r\n    setTimeout(() => setAutoScrollEnabled(true), 2000); // Resume auto-scroll after 2s\r\n  };\r\n \r\n  // Touch handlers for mobile\r\n  const handleTouchStart = (e) => {\r\n    setIsDragging(true);\r\n    setAutoScrollEnabled(false);\r\n    setStartX(e.touches[0].pageX - sliderRef.current.offsetLeft);\r\n    setScrollLeft(sliderRef.current.scrollLeft);\r\n  };\r\n\r\n  const handleTouchMove = (e) => {\r\n    if (!isDragging) return;\r\n    const x = e.touches[0].pageX - sliderRef.current.offsetLeft;\r\n    const walk = (x - startX) * 2;\r\n    sliderRef.current.scrollLeft = scrollLeft - walk;\r\n  };\r\n\r\n  const handleTouchEnd = () => {\r\n    setIsDragging(false);\r\n    setTimeout(() => setAutoScrollEnabled(true), 2000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative overflow-hidden\">\r\n      <div\r\n        ref={sliderRef}\r\n        className=\"flex gap-4 overflow-x-auto scrollbar-hide cursor-grab active:cursor-grabbing\"\r\n        style={{\r\n          scrollbarWidth: 'none',\r\n          msOverflowStyle: 'none',\r\n          WebkitOverflowScrolling: 'touch'\r\n        }}\r\n        onMouseDown={handleMouseDown}\r\n        onMouseMove={handleMouseMove}\r\n        onMouseUp={handleMouseUp}\r\n        onMouseLeave={handleMouseUp}\r\n        onTouchStart={handleTouchStart}\r\n        onTouchMove={handleTouchMove}\r\n        onTouchEnd={handleTouchEnd}\r\n      >\r\n        {infiniteShifts.map((shift, index) => (\r\n          <div key={`${shift.name}-${index}`} className=\"flex-shrink-0 w-80 sm:w-72 md:w-80\">\r\n            <ShiftCard shift={shift} />\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Gradient overlays for smooth edges */}\r\n      <div className=\"absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 to-transparent pointer-events-none\" />\r\n      <div className=\"absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 to-transparent pointer-events-none\" />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ShiftSummarySection = () => {\r\n  const [shifts, setShifts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const fetchShiftData = async () => {\r\n      const token = localStorage.getItem(\"token\");\r\n      if (!token) {\r\n        setError(\"No auth token found\");\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setLoading(true);\r\n\r\n        // Fetch users data to calculate role counts per shift\r\n        const usersResponse = await fetch(`${API_URL}/list/users-by-default-team`, {\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n            'Accept': 'application/json'\r\n          },\r\n        });\r\n\r\n        let usersData = [];\r\n        if (usersResponse.ok) {\r\n          usersData = await usersResponse.json();\r\n        }\r\n\r\n        // Fetch schedule data to get actual shift assignments\r\n        const scheduleResponse = await fetch(`${API_URL}/schedule-data`, {\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n            'Accept': 'application/json'\r\n          },\r\n        });\r\n\r\n        let scheduleData = [];\r\n        if (scheduleResponse.ok) {\r\n          const scheduleResult = await scheduleResponse.json();\r\n          scheduleData = scheduleResult?.data || [];\r\n        }\r\n\r\n        // Fetch time cards data to get shift-based work data\r\n        let timeCardsData = [];\r\n        try {\r\n          const timeCardsResponse = await fetch(`${API_URL}/time-cards-data?per_page=1000`, {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json',\r\n              'Accept': 'application/json'\r\n            },\r\n          });\r\n\r\n          if (timeCardsResponse.ok) {\r\n            const timeCardsResult = await timeCardsResponse.json();\r\n            // Handle different possible response structures\r\n            if (Array.isArray(timeCardsResult)) {\r\n              timeCardsData = timeCardsResult;\r\n            } else if (timeCardsResult?.data?.data && Array.isArray(timeCardsResult.data.data)) {\r\n              // Paginated response: { data: { data: [...], current_page: 1, ... } }\r\n              timeCardsData = timeCardsResult.data.data;\r\n            } else if (timeCardsResult?.data && Array.isArray(timeCardsResult.data)) {\r\n              timeCardsData = timeCardsResult.data;\r\n            } else if (timeCardsResult?.timeCards && Array.isArray(timeCardsResult.timeCards)) {\r\n              timeCardsData = timeCardsResult.timeCards;\r\n            } else {\r\n              console.warn('Unexpected time cards response structure:', timeCardsResult);\r\n              timeCardsData = [];\r\n            }\r\n            console.log(\"Time Cards Data:\", timeCardsData);\r\n            \r\n          }\r\n        } catch (timeCardError) {\r\n          console.warn('Failed to fetch time cards data:', timeCardError);\r\n          timeCardsData = [];\r\n        }\r\n\r\n        // Process shift data with real user counts\r\n        const processedShifts = [];\r\n        const shiftNames = ['Evening', 'Morning', 'Night', 'Custom'];\r\n\r\n        shiftNames.forEach(shiftName => {\r\n          let shiftUsers = [];\r\n\r\n          if (shiftName === 'Custom') {\r\n            // For custom shift, get users from schedule data with custom shift names\r\n            const customSchedules = scheduleData.filter(schedule =>\r\n              schedule.shift_name &&\r\n              !['Evening', 'Morning', 'Night'].includes(schedule.shift_name)\r\n            );\r\n\r\n            const customUserIds = customSchedules.map(schedule => schedule.user_id).filter(Boolean);\r\n            shiftUsers = usersData.filter(user => customUserIds.includes(user.id));\r\n          } else {\r\n            // For standard shifts, try to get from schedule data first\r\n            const shiftSchedules = scheduleData.filter(schedule =>\r\n              schedule.shift_name?.toLowerCase() === shiftName.toLowerCase()\r\n            );\r\n\r\n            if (shiftSchedules.length > 0) {\r\n              const shiftUserIds = shiftSchedules.map(schedule => schedule.user_id).filter(Boolean);\r\n              shiftUsers = usersData.filter(user => shiftUserIds.includes(user.id));\r\n            } else {\r\n              // Fallback: distribute users across shifts based on time cards or index\r\n              const shiftTimeCards = Array.isArray(timeCardsData)\r\n                ? timeCardsData.filter(timeCard => {\r\n                  if (timeCard.shift_id) {\r\n                    // If shift_id exists, try to match with schedule\r\n                    const matchingSchedule = scheduleData.find(schedule =>\r\n                      schedule.id === timeCard.shift_id &&\r\n                      schedule.shift_name?.toLowerCase() === shiftName.toLowerCase()\r\n                    );\r\n                    return !!matchingSchedule;\r\n                  }\r\n                  return false;\r\n                })\r\n                : [];\r\n\r\n              if (shiftTimeCards.length > 0) {\r\n                const shiftUserIds = [...new Set(shiftTimeCards.map(tc => tc.user_id))];\r\n                shiftUsers = usersData.filter(user => shiftUserIds.includes(user.id));\r\n              } else {\r\n                // Final fallback: distribute by index\r\n                shiftUsers = usersData.filter((_, index) => {\r\n                  if (shiftName === 'Evening') return index % 3 === 0;\r\n                  if (shiftName === 'Morning') return index % 3 === 1;\r\n                  if (shiftName === 'Night') return index % 3 === 2;\r\n                  return false;\r\n                });\r\n              }\r\n            }\r\n          }\r\n\r\n          // Count by designation\r\n          const designerCount = shiftUsers.filter(user =>\r\n            user.designations?.[0]?.name?.toLowerCase().includes('designer') ||\r\n            user.designations?.[0]?.name?.toLowerCase().includes('design')\r\n          ).length;\r\n\r\n          const developerCount = shiftUsers.filter(user =>\r\n            user.designations?.[0]?.name?.toLowerCase().includes('developer') ||\r\n            user.designations?.[0]?.name?.toLowerCase().includes('engineer') ||\r\n            user.designations?.[0]?.name?.toLowerCase().includes('dev')\r\n          ).length;\r\n\r\n          const qaCount = shiftUsers.filter(user =>\r\n            user.designations?.[0]?.name?.toLowerCase().includes('qa') ||\r\n            user.designations?.[0]?.name?.toLowerCase().includes('quality') ||\r\n            user.designations?.[0]?.name?.toLowerCase().includes('test') ||\r\n            user.designations?.[0]?.name?.toLowerCase().includes('assurance')\r\n          ).length;\r\n\r\n          processedShifts.push({\r\n            name: shiftName,\r\n            designer: designerCount || 0,\r\n            developer: developerCount || 0,\r\n            qa: qaCount || 0\r\n          });\r\n        });\r\n\r\n        \r\n\r\n        // Sort by preferred order\r\n        const order = ['evening', 'morning', 'night', 'custom'];\r\n        processedShifts.sort((a, b) =>\r\n          order.indexOf(a.name.toLowerCase()) - order.indexOf(b.name.toLowerCase())\r\n        );\r\n\r\n        setShifts(processedShifts);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error(\"Error fetching shift data:\", err);\r\n        setError(\"Unable to load shift data. Please try again later.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchShiftData();\r\n  }, []);\r\n\r\n  if (loading) return <Loading />;\r\n  if (error) return <div className=\"text-red-500 text-center py-4\">{error}</div>;\r\n  if (!shifts.length) return <div className=\"text-gray-500 text-center py-4\">No shift data available</div>;\r\n\r\n  return <InfiniteShiftSlider shifts={shifts} />;\r\n};\r\n\r\n// Add CSS for hiding scrollbars\r\nconst style = document.createElement('style');\r\nstyle.textContent = `\r\n  .scrollbar-hide {\r\n    -ms-overflow-style: none;\r\n    scrollbar-width: none;\r\n  }\r\n  .scrollbar-hide::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n`;\r\nif (!document.head.querySelector('style[data-scrollbar-hide-shifts]')) {\r\n  style.setAttribute('data-scrollbar-hide-shifts', 'true');\r\n  document.head.appendChild(style);\r\n}\r\n\r\nexport default ShiftSummarySection;"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,OAAO,QAAQ,+BAA+B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAM,CAAC,kBACjCH,OAAA;EAAKI,SAAS,EAAC,mIAAmI;EAAAC,QAAA,gBAChJL,OAAA;IAAKI,SAAS,EAAC,8CAA8C;IAAAC,QAAA,EAAEH;EAAK;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,eAC3ET,OAAA;IAAKI,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBAEvEL,OAAA;MAAKU,KAAK,EAAC,4BAA4B;MAACC,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAACC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACd,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC5NL,OAAA;QAAMmB,CAAC,EAAC;MAA2C;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC3DT,OAAA;QAAQoB,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,CAAC,EAAC;MAAG;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eACNT,OAAA;MAAMI,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAEkB,MAAM,CAACpB,KAAK,CAAC,CAACqB,QAAQ,CAAC,CAAC,EAAE,GAAG;IAAC;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5E,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACgB,EAAA,GAZIxB,SAAS;AAcf,MAAMyB,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,kBAC1B3B,OAAA;EAAKI,SAAS,EAAC,uFAAuF;EAAAC,QAAA,gBACpGL,OAAA;IAAII,SAAS,EAAC,6DAA6D;IAAAC,QAAA,GAAEsB,KAAK,CAACC,IAAI,EAAC,QAAM;EAAA;IAAAtB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACnGT,OAAA;IAAKI,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBACpDL,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,gBAAgB;MAACC,KAAK,EAAEwB,KAAK,CAACE;IAAS;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3DT,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,iBAAiB;MAACC,KAAK,EAAEwB,KAAK,CAACG;IAAU;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7DT,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,UAAU;MAACC,KAAK,EAAEwB,KAAK,CAACI;IAAG;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5C,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAuB,GAAA,GAXMN,SAAS;AAYf,MAAMO,mBAAmB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAMC,SAAS,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAMkD,cAAc,GAAG,CAAC,GAAGX,MAAM,EAAE,GAAGA,MAAM,EAAE,GAAGA,MAAM,EAAE,GAAGA,MAAM,CAAC;;EAEnE;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI,CAACiD,iBAAiB,IAAIN,UAAU,IAAIH,MAAM,CAACY,MAAM,KAAK,CAAC,EAAE;IAE7D,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAIZ,SAAS,CAACa,OAAO,EAAE;QACrB,MAAMC,MAAM,GAAGd,SAAS,CAACa,OAAO;QAChC,MAAME,SAAS,GAAG,GAAG,CAAC,CAAC;QACvB,MAAMC,SAAS,GAAGD,SAAS,GAAGjB,MAAM,CAACY,MAAM;;QAE3C;QACA,IAAII,MAAM,CAACT,UAAU,IAAIW,SAAS,EAAE;UAClCF,MAAM,CAACG,QAAQ,CAAC;YAAEC,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAO,CAAC,CAAC;QAChD,CAAC,MAAM;UACLL,MAAM,CAACT,UAAU,IAAI,GAAG;QAC1B;MACF;IACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;IAER,OAAO,MAAMe,aAAa,CAACT,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACJ,iBAAiB,EAAEN,UAAU,EAAEH,MAAM,CAACY,MAAM,CAAC,CAAC;;EAElD;EACA,MAAMW,eAAe,GAAIC,CAAC,IAAK;IAC7BpB,aAAa,CAAC,IAAI,CAAC;IACnBM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,SAAS,CAACkB,CAAC,CAACC,KAAK,GAAGvB,SAAS,CAACa,OAAO,CAACW,UAAU,CAAC;IACjDlB,aAAa,CAACN,SAAS,CAACa,OAAO,CAACR,UAAU,CAAC;EAC7C,CAAC;EAED,MAAMoB,eAAe,GAAIH,CAAC,IAAK;IAC7B,IAAI,CAACrB,UAAU,EAAE;IACjBqB,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMC,CAAC,GAAGL,CAAC,CAACC,KAAK,GAAGvB,SAAS,CAACa,OAAO,CAACW,UAAU;IAChD,MAAMI,IAAI,GAAG,CAACD,CAAC,GAAGxB,MAAM,IAAI,CAAC;IAC7BH,SAAS,CAACa,OAAO,CAACR,UAAU,GAAGA,UAAU,GAAGuB,IAAI;EAClD,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B3B,aAAa,CAAC,KAAK,CAAC;IACpB4B,UAAU,CAAC,MAAMtB,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAMuB,gBAAgB,GAAIT,CAAC,IAAK;IAC9BpB,aAAa,CAAC,IAAI,CAAC;IACnBM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,SAAS,CAACkB,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,CAACT,KAAK,GAAGvB,SAAS,CAACa,OAAO,CAACW,UAAU,CAAC;IAC5DlB,aAAa,CAACN,SAAS,CAACa,OAAO,CAACR,UAAU,CAAC;EAC7C,CAAC;EAED,MAAM4B,eAAe,GAAIX,CAAC,IAAK;IAC7B,IAAI,CAACrB,UAAU,EAAE;IACjB,MAAM0B,CAAC,GAAGL,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,CAACT,KAAK,GAAGvB,SAAS,CAACa,OAAO,CAACW,UAAU;IAC3D,MAAMI,IAAI,GAAG,CAACD,CAAC,GAAGxB,MAAM,IAAI,CAAC;IAC7BH,SAAS,CAACa,OAAO,CAACR,UAAU,GAAGA,UAAU,GAAGuB,IAAI;EAClD,CAAC;EAED,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3BhC,aAAa,CAAC,KAAK,CAAC;IACpB4B,UAAU,CAAC,MAAMtB,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EACpD,CAAC;EAED,oBACE5C,OAAA;IAAKI,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCL,OAAA;MACEuE,GAAG,EAAEnC,SAAU;MACfhC,SAAS,EAAC,8EAA8E;MACxFoE,KAAK,EAAE;QACLC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE,MAAM;QACvBC,uBAAuB,EAAE;MAC3B,CAAE;MACFC,WAAW,EAAEnB,eAAgB;MAC7BoB,WAAW,EAAEhB,eAAgB;MAC7BiB,SAAS,EAAEb,aAAc;MACzBc,YAAY,EAAEd,aAAc;MAC5Be,YAAY,EAAEb,gBAAiB;MAC/Bc,WAAW,EAAEZ,eAAgB;MAC7Ba,UAAU,EAAEZ,cAAe;MAAAjE,QAAA,EAE1BwC,cAAc,CAACsC,GAAG,CAAC,CAACxD,KAAK,EAAEyD,KAAK,kBAC/BpF,OAAA;QAAoCI,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eAChFL,OAAA,CAAC0B,SAAS;UAACC,KAAK,EAAEA;QAAM;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADnB,GAAGkB,KAAK,CAACC,IAAI,IAAIwD,KAAK,EAAE;QAAA9E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7B,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNT,OAAA;MAAKI,SAAS,EAAC;IAAsG;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxHT,OAAA;MAAKI,SAAS,EAAC;IAAuG;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtH,CAAC;AAEV,CAAC;AAAC0B,EAAA,CAvGIF,mBAAmB;AAAAoD,GAAA,GAAnBpD,mBAAmB;AAyGzB,MAAMqD,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAChC,MAAM,CAACrD,MAAM,EAAEsD,SAAS,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8F,OAAO,EAAEC,UAAU,CAAC,GAAG/F,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgG,KAAK,EAAEC,QAAQ,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EAExCD,SAAS,CAAC,MAAM;IACd,MAAMmG,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVF,QAAQ,CAAC,qBAAqB,CAAC;QAC/BF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACFA,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMO,aAAa,GAAG,MAAMC,KAAK,CAAC,GAAGpG,OAAO,6BAA6B,EAAE;UACzEqG,OAAO,EAAE;YACP,eAAe,EAAE,UAAUL,KAAK,EAAE;YAClC,cAAc,EAAE,kBAAkB;YAClC,QAAQ,EAAE;UACZ;QACF,CAAC,CAAC;QAEF,IAAIM,SAAS,GAAG,EAAE;QAClB,IAAIH,aAAa,CAACI,EAAE,EAAE;UACpBD,SAAS,GAAG,MAAMH,aAAa,CAACK,IAAI,CAAC,CAAC;QACxC;;QAEA;QACA,MAAMC,gBAAgB,GAAG,MAAML,KAAK,CAAC,GAAGpG,OAAO,gBAAgB,EAAE;UAC/DqG,OAAO,EAAE;YACP,eAAe,EAAE,UAAUL,KAAK,EAAE;YAClC,cAAc,EAAE,kBAAkB;YAClC,QAAQ,EAAE;UACZ;QACF,CAAC,CAAC;QAEF,IAAIU,YAAY,GAAG,EAAE;QACrB,IAAID,gBAAgB,CAACF,EAAE,EAAE;UACvB,MAAMI,cAAc,GAAG,MAAMF,gBAAgB,CAACD,IAAI,CAAC,CAAC;UACpDE,YAAY,GAAG,CAAAC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEC,IAAI,KAAI,EAAE;QAC3C;;QAEA;QACA,IAAIC,aAAa,GAAG,EAAE;QACtB,IAAI;UACF,MAAMC,iBAAiB,GAAG,MAAMV,KAAK,CAAC,GAAGpG,OAAO,gCAAgC,EAAE;YAChFqG,OAAO,EAAE;cACP,eAAe,EAAE,UAAUL,KAAK,EAAE;cAClC,cAAc,EAAE,kBAAkB;cAClC,QAAQ,EAAE;YACZ;UACF,CAAC,CAAC;UAEF,IAAIc,iBAAiB,CAACP,EAAE,EAAE;YAAA,IAAAQ,qBAAA;YACxB,MAAMC,eAAe,GAAG,MAAMF,iBAAiB,CAACN,IAAI,CAAC,CAAC;YACtD;YACA,IAAIS,KAAK,CAACC,OAAO,CAACF,eAAe,CAAC,EAAE;cAClCH,aAAa,GAAGG,eAAe;YACjC,CAAC,MAAM,IAAIA,eAAe,aAAfA,eAAe,gBAAAD,qBAAA,GAAfC,eAAe,CAAEJ,IAAI,cAAAG,qBAAA,eAArBA,qBAAA,CAAuBH,IAAI,IAAIK,KAAK,CAACC,OAAO,CAACF,eAAe,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;cAClF;cACAC,aAAa,GAAGG,eAAe,CAACJ,IAAI,CAACA,IAAI;YAC3C,CAAC,MAAM,IAAII,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEJ,IAAI,IAAIK,KAAK,CAACC,OAAO,CAACF,eAAe,CAACJ,IAAI,CAAC,EAAE;cACvEC,aAAa,GAAGG,eAAe,CAACJ,IAAI;YACtC,CAAC,MAAM,IAAII,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEG,SAAS,IAAIF,KAAK,CAACC,OAAO,CAACF,eAAe,CAACG,SAAS,CAAC,EAAE;cACjFN,aAAa,GAAGG,eAAe,CAACG,SAAS;YAC3C,CAAC,MAAM;cACLC,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEL,eAAe,CAAC;cAC1EH,aAAa,GAAG,EAAE;YACpB;YACAO,OAAO,CAACE,GAAG,CAAC,kBAAkB,EAAET,aAAa,CAAC;UAEhD;QACF,CAAC,CAAC,OAAOU,aAAa,EAAE;UACtBH,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEE,aAAa,CAAC;UAC/DV,aAAa,GAAG,EAAE;QACpB;;QAEA;QACA,MAAMW,eAAe,GAAG,EAAE;QAC1B,MAAMC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;QAE5DA,UAAU,CAACC,OAAO,CAACC,SAAS,IAAI;UAC9B,IAAIC,UAAU,GAAG,EAAE;UAEnB,IAAID,SAAS,KAAK,QAAQ,EAAE;YAC1B;YACA,MAAME,eAAe,GAAGnB,YAAY,CAACoB,MAAM,CAACC,QAAQ,IAClDA,QAAQ,CAACC,UAAU,IACnB,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACF,QAAQ,CAACC,UAAU,CAC/D,CAAC;YAED,MAAME,aAAa,GAAGL,eAAe,CAACxC,GAAG,CAAC0C,QAAQ,IAAIA,QAAQ,CAACI,OAAO,CAAC,CAACL,MAAM,CAACM,OAAO,CAAC;YACvFR,UAAU,GAAGtB,SAAS,CAACwB,MAAM,CAACO,IAAI,IAAIH,aAAa,CAACD,QAAQ,CAACI,IAAI,CAACC,EAAE,CAAC,CAAC;UACxE,CAAC,MAAM;YACL;YACA,MAAMC,cAAc,GAAG7B,YAAY,CAACoB,MAAM,CAACC,QAAQ;cAAA,IAAAS,oBAAA;cAAA,OACjD,EAAAA,oBAAA,GAAAT,QAAQ,CAACC,UAAU,cAAAQ,oBAAA,uBAAnBA,oBAAA,CAAqBC,WAAW,CAAC,CAAC,MAAKd,SAAS,CAACc,WAAW,CAAC,CAAC;YAAA,CAChE,CAAC;YAED,IAAIF,cAAc,CAACvF,MAAM,GAAG,CAAC,EAAE;cAC7B,MAAM0F,YAAY,GAAGH,cAAc,CAAClD,GAAG,CAAC0C,QAAQ,IAAIA,QAAQ,CAACI,OAAO,CAAC,CAACL,MAAM,CAACM,OAAO,CAAC;cACrFR,UAAU,GAAGtB,SAAS,CAACwB,MAAM,CAACO,IAAI,IAAIK,YAAY,CAACT,QAAQ,CAACI,IAAI,CAACC,EAAE,CAAC,CAAC;YACvE,CAAC,MAAM;cACL;cACA,MAAMK,cAAc,GAAG1B,KAAK,CAACC,OAAO,CAACL,aAAa,CAAC,GAC/CA,aAAa,CAACiB,MAAM,CAACc,QAAQ,IAAI;gBACjC,IAAIA,QAAQ,CAACC,QAAQ,EAAE;kBACrB;kBACA,MAAMC,gBAAgB,GAAGpC,YAAY,CAACqC,IAAI,CAAChB,QAAQ;oBAAA,IAAAiB,qBAAA;oBAAA,OACjDjB,QAAQ,CAACO,EAAE,KAAKM,QAAQ,CAACC,QAAQ,IACjC,EAAAG,qBAAA,GAAAjB,QAAQ,CAACC,UAAU,cAAAgB,qBAAA,uBAAnBA,qBAAA,CAAqBP,WAAW,CAAC,CAAC,MAAKd,SAAS,CAACc,WAAW,CAAC,CAAC;kBAAA,CAChE,CAAC;kBACD,OAAO,CAAC,CAACK,gBAAgB;gBAC3B;gBACA,OAAO,KAAK;cACd,CAAC,CAAC,GACA,EAAE;cAEN,IAAIH,cAAc,CAAC3F,MAAM,GAAG,CAAC,EAAE;gBAC7B,MAAM0F,YAAY,GAAG,CAAC,GAAG,IAAIO,GAAG,CAACN,cAAc,CAACtD,GAAG,CAAC6D,EAAE,IAAIA,EAAE,CAACf,OAAO,CAAC,CAAC,CAAC;gBACvEP,UAAU,GAAGtB,SAAS,CAACwB,MAAM,CAACO,IAAI,IAAIK,YAAY,CAACT,QAAQ,CAACI,IAAI,CAACC,EAAE,CAAC,CAAC;cACvE,CAAC,MAAM;gBACL;gBACAV,UAAU,GAAGtB,SAAS,CAACwB,MAAM,CAAC,CAACqB,CAAC,EAAE7D,KAAK,KAAK;kBAC1C,IAAIqC,SAAS,KAAK,SAAS,EAAE,OAAOrC,KAAK,GAAG,CAAC,KAAK,CAAC;kBACnD,IAAIqC,SAAS,KAAK,SAAS,EAAE,OAAOrC,KAAK,GAAG,CAAC,KAAK,CAAC;kBACnD,IAAIqC,SAAS,KAAK,OAAO,EAAE,OAAOrC,KAAK,GAAG,CAAC,KAAK,CAAC;kBACjD,OAAO,KAAK;gBACd,CAAC,CAAC;cACJ;YACF;UACF;;UAEA;UACA,MAAM8D,aAAa,GAAGxB,UAAU,CAACE,MAAM,CAACO,IAAI;YAAA,IAAAgB,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA;YAAA,OAC1C,EAAAL,kBAAA,GAAAhB,IAAI,CAACsB,YAAY,cAAAN,kBAAA,wBAAAC,mBAAA,GAAjBD,kBAAA,CAAoB,CAAC,CAAC,cAAAC,mBAAA,wBAAAC,qBAAA,GAAtBD,mBAAA,CAAwBxH,IAAI,cAAAyH,qBAAA,uBAA5BA,qBAAA,CAA8Bd,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,UAAU,CAAC,OAAAuB,mBAAA,GAChEnB,IAAI,CAACsB,YAAY,cAAAH,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwB3H,IAAI,cAAA4H,qBAAA,uBAA5BA,qBAAA,CAA8BjB,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,QAAQ,CAAC;UAAA,CAChE,CAAC,CAACjF,MAAM;UAER,MAAM4G,cAAc,GAAGhC,UAAU,CAACE,MAAM,CAACO,IAAI;YAAA,IAAAwB,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA;YAAA,OAC3C,EAAAR,mBAAA,GAAAxB,IAAI,CAACsB,YAAY,cAAAE,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwBhI,IAAI,cAAAiI,qBAAA,uBAA5BA,qBAAA,CAA8BtB,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,WAAW,CAAC,OAAA+B,mBAAA,GACjE3B,IAAI,CAACsB,YAAY,cAAAK,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwBnI,IAAI,cAAAoI,qBAAA,uBAA5BA,qBAAA,CAA8BzB,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,UAAU,CAAC,OAAAkC,mBAAA,GAChE9B,IAAI,CAACsB,YAAY,cAAAQ,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwBtI,IAAI,cAAAuI,qBAAA,uBAA5BA,qBAAA,CAA8B5B,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,KAAK,CAAC;UAAA,CAC7D,CAAC,CAACjF,MAAM;UAER,MAAMsH,OAAO,GAAG1C,UAAU,CAACE,MAAM,CAACO,IAAI;YAAA,IAAAkC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA;YAAA,OACpC,EAAAX,mBAAA,GAAAlC,IAAI,CAACsB,YAAY,cAAAY,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwB1I,IAAI,cAAA2I,qBAAA,uBAA5BA,qBAAA,CAA8BhC,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,IAAI,CAAC,OAAAyC,mBAAA,GAC1DrC,IAAI,CAACsB,YAAY,cAAAe,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwB7I,IAAI,cAAA8I,qBAAA,uBAA5BA,qBAAA,CAA8BnC,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,SAAS,CAAC,OAAA4C,mBAAA,GAC/DxC,IAAI,CAACsB,YAAY,cAAAkB,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwBhJ,IAAI,cAAAiJ,qBAAA,uBAA5BA,qBAAA,CAA8BtC,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,MAAM,CAAC,OAAA+C,mBAAA,GAC5D3C,IAAI,CAACsB,YAAY,cAAAqB,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwBnJ,IAAI,cAAAoJ,qBAAA,uBAA5BA,qBAAA,CAA8BzC,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC,WAAW,CAAC;UAAA,CACnE,CAAC,CAACjF,MAAM;UAERwE,eAAe,CAAC2D,IAAI,CAAC;YACnBrJ,IAAI,EAAE6F,SAAS;YACf5F,QAAQ,EAAEqH,aAAa,IAAI,CAAC;YAC5BpH,SAAS,EAAE4H,cAAc,IAAI,CAAC;YAC9B3H,EAAE,EAAEqI,OAAO,IAAI;UACjB,CAAC,CAAC;QACJ,CAAC,CAAC;;QAIF;QACA,MAAMc,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;QACvD5D,eAAe,CAAC6D,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACxBH,KAAK,CAACI,OAAO,CAACF,CAAC,CAACxJ,IAAI,CAAC2G,WAAW,CAAC,CAAC,CAAC,GAAG2C,KAAK,CAACI,OAAO,CAACD,CAAC,CAACzJ,IAAI,CAAC2G,WAAW,CAAC,CAAC,CAC1E,CAAC;QAED/C,SAAS,CAAC8B,eAAe,CAAC;QAC1B1B,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAO2F,GAAG,EAAE;QACZrE,OAAO,CAACvB,KAAK,CAAC,4BAA4B,EAAE4F,GAAG,CAAC;QAChD3F,QAAQ,CAAC,oDAAoD,CAAC;MAChE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIJ,OAAO,EAAE,oBAAOzF,OAAA,CAACH,OAAO;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC/B,IAAIkF,KAAK,EAAE,oBAAO3F,OAAA;IAAKI,SAAS,EAAC,+BAA+B;IAAAC,QAAA,EAAEsF;EAAK;IAAArF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9E,IAAI,CAACyB,MAAM,CAACY,MAAM,EAAE,oBAAO9C,OAAA;IAAKI,SAAS,EAAC,gCAAgC;IAAAC,QAAA,EAAC;EAAuB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAExG,oBAAOT,OAAA,CAACiC,mBAAmB;IAACC,MAAM,EAAEA;EAAO;IAAA5B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAChD,CAAC;;AAED;AAAA8E,GAAA,CAhMMD,mBAAmB;AAAAkG,GAAA,GAAnBlG,mBAAmB;AAiMzB,MAAMd,KAAK,GAAGiH,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;AAC7ClH,KAAK,CAACmH,WAAW,GAAG;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACC,aAAa,CAAC,mCAAmC,CAAC,EAAE;EACrErH,KAAK,CAACsH,YAAY,CAAC,4BAA4B,EAAE,MAAM,CAAC;EACxDL,QAAQ,CAACG,IAAI,CAACG,WAAW,CAACvH,KAAK,CAAC;AAClC;AAEA,eAAec,mBAAmB;AAAC,IAAA7D,EAAA,EAAAO,GAAA,EAAAqD,GAAA,EAAAmG,GAAA;AAAAQ,YAAA,CAAAvK,EAAA;AAAAuK,YAAA,CAAAhK,GAAA;AAAAgK,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAR,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}